/**
 * AJAX Filter Module cho trang products.php
 * <PERSON><PERSON> lý việc filter sản phẩm không cần reload trang
 */

class AjaxFilter {
    constructor() {
        console.log('🚀 AJAX Filter: Constructor called');
        this.apiUrl = 'api/filter-products.php';
        this.isLoading = false;
        this.currentPage = 1;
        console.log('✅ AJAX Filter: Properties initialized');
        this.init();
    }

    init() {
        console.log('AJAX Filter: Init called');
        this.bindEvents();
        this.setupHistoryHandling();
        console.log('AJAX Filter: Init completed');
    }

    bindEvents() {
        console.log('AJAX Filter: bindEvents called');

        // Đ<PERSON>h dấu rằng AJAX filter đã được khởi tạo
        window.ajaxFilterActive = true;
        console.log('AJAX Filter: ajaxFilterActive flag set');

        // Skip window.location override - not needed and causes errors

        // Override tất cả event handlers ch<PERSON> <PERSON><PERSON><PERSON> apply filters
        this.overrideApplyFiltersButton();

        // Bind main search form events
        this.bindMainSearchEvents();

        // Bind sort and items per page events
        this.bindSortAndPaginationEvents();

        console.log('AJAX Filter: bindEvents completed');

        // Bind event cho pagination (sẽ được thêm sau khi load sản phẩm)
        document.addEventListener('click', (e) => {
            if (e.target.matches('.ajax-pagination-link')) {
                e.preventDefault();
                const page = parseInt(e.target.dataset.page);
                if (page && !this.isLoading) {
                    this.loadProducts(this.collectFilterData(), page);
                }
            }
        });
    }

    bindMainSearchEvents() {
        console.log('🔍 AJAX Filter: bindMainSearchEvents called');

        // Tìm form tìm kiếm chính
        const mainSearchForm = document.querySelector('form.search-container-solution1');
        const mainSearchInput = document.getElementById('main-search-input');
        const searchSubmitBtn = document.getElementById('search-submit-btn');

        console.log('🔍 AJAX Filter: Elements found:', {
            mainSearchForm: !!mainSearchForm,
            mainSearchInput: !!mainSearchInput,
            searchSubmitBtn: !!searchSubmitBtn
        });

        if (!mainSearchForm) {
            console.warn('❌ AJAX Filter: Main search form not found');
            return;
        }

        console.log('✅ AJAX Filter: Found main search form, binding events');

        const self = this;

        // Override form submission
        mainSearchForm.addEventListener('submit', function(e) {
            console.log('AJAX Filter: Main search form submitted');
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();

            if (!self.isLoading) {
                self.handleMainSearchSubmit();
            }
            return false;
        }, true);

        // Override search button click
        if (searchSubmitBtn) {
            searchSubmitBtn.addEventListener('click', function(e) {
                console.log('AJAX Filter: Search submit button clicked');
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();

                if (!self.isLoading) {
                    self.handleMainSearchSubmit();
                }
                return false;
            }, true);
        }

        // Handle Enter key in search input
        if (mainSearchInput) {
            mainSearchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    console.log('AJAX Filter: Enter key pressed in search input');
                    e.preventDefault();
                    e.stopPropagation();

                    if (!self.isLoading) {
                        self.handleMainSearchSubmit();
                    }
                    return false;
                }
            });
        }

        console.log('AJAX Filter: Main search events bound successfully');
    }

    overrideApplyFiltersButton() {
        const applyFiltersBtn = document.getElementById('applyFilters');
        if (!applyFiltersBtn) {
            console.warn('AJAX Filter: Apply filters button not found');
            return;
        }

        console.log('AJAX Filter: Overriding apply filters button');

        // Thêm attribute để đánh dấu
        applyFiltersBtn.setAttribute('data-ajax-filter', 'true');

        // Override onclick property để chặn tất cả handlers khác
        const self = this;
        applyFiltersBtn.onclick = function(e) {
            console.log('AJAX Filter: onclick handler called');
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            self.handleFilterSubmit();
            return false;
        };

        // Thêm event listener với capture phase
        applyFiltersBtn.addEventListener('click', function(e) {
            console.log('AJAX Filter: addEventListener handler called');
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            self.handleFilterSubmit();
            return false;
        }, true);

        // Override form submission nếu button nằm trong form
        const form = applyFiltersBtn.closest('form');
        if (form) {
            console.log('AJAX Filter: Overriding form submission');
            form.addEventListener('submit', (e) => {
                console.log('AJAX Filter: Form submit prevented');
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                return false;
            }, true);
        }
    }



    setupHistoryHandling() {
        // Xử lý back/forward button của browser
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.isAjaxFilter) {
                this.loadProducts(e.state.filterData, e.state.page, false);
            }
        });
    }

    collectFilterData(ignoreUrlKeyword = false) {
        const data = {};

        // Keyword - ưu tiên main search input, sau đó sidebar search, cuối cùng là URL
        const mainSearchInput = document.getElementById('main-search-input');
        const sidebarSearchInput = document.querySelector('input[name="keyword"], #search-input');
        const urlParams = new URLSearchParams(window.location.search);
        const urlKeyword = urlParams.get('keyword');

        console.log('🔍 AJAX Filter: collectFilterData called, ignoreUrlKeyword:', ignoreUrlKeyword);

        let keyword = '';

        // Ưu tiên 1: Main search input
        if (mainSearchInput && mainSearchInput.value.trim()) {
            keyword = mainSearchInput.value.trim();
            console.log('✅ Using keyword from main search input:', keyword);
        }
        // Ưu tiên 2: Sidebar search input (nếu khác với main search)
        else if (sidebarSearchInput && sidebarSearchInput !== mainSearchInput && sidebarSearchInput.value.trim()) {
            keyword = sidebarSearchInput.value.trim();
            console.log('✅ Using keyword from sidebar search input:', keyword);
        }
        // Ưu tiên 3: URL keyword (chỉ khi không bị ignore)
        else if (!ignoreUrlKeyword && urlKeyword && urlKeyword.trim()) {
            keyword = urlKeyword.trim();
            console.log('✅ Using keyword from URL:', keyword);
        }

        if (keyword) {
            data.keyword = keyword;
        }

        console.log('🔍 AJAX Filter: Final keyword in data:', data.keyword || 'None');

        // Categories
        const selectedCategories = Array.from(document.querySelectorAll('input[name="category[]"]:checked'))
            .map(input => parseInt(input.value))
            .filter(id => id > 0);
        if (selectedCategories.length > 0) {
            data.categories = selectedCategories;
        }

        // Price range
        const priceMinInput = document.getElementById('price-min');
        const priceMaxInput = document.getElementById('price-max');
        
        if (priceMinInput && priceMinInput.value.trim()) {
            const minPrice = this.getPriceNumericValue(priceMinInput);
            if (minPrice > 0) {
                data.price_min = minPrice;
            }
        }
        
        if (priceMaxInput && priceMaxInput.value.trim()) {
            const maxPrice = this.getPriceNumericValue(priceMaxInput);
            if (maxPrice > 0) {
                data.price_max = maxPrice;
            }
        }

        // Promotions
        const selectedPromotions = Array.from(document.querySelectorAll('input[name="promotion[]"]:checked'))
            .map(input => input.value);
        if (selectedPromotions.length > 0) {
            data.promotions = selectedPromotions;
        }

        // Sort
        const sortSelect = document.getElementById('sort-select');
        if (sortSelect && sortSelect.value) {
            data.sort = sortSelect.value;
        } else {
            // Default sort
            data.sort = 'newest';
        }

        // Items per page
        const itemsPerPageSelect = document.getElementById('items-per-page');
        if (itemsPerPageSelect && itemsPerPageSelect.value) {
            data.items_per_page = parseInt(itemsPerPageSelect.value);
        } else {
            // Default items per page
            data.items_per_page = 12;
        }

        return data;
    }

    getPriceNumericValue(input) {
        if (!input || !input.value) return 0;
        
        // Loại bỏ tất cả ký tự không phải số
        const numericValue = input.value.replace(/[^\d]/g, '');
        return parseInt(numericValue) || 0;
    }

    hasActiveFilters(data) {
        const result = !!(data.keyword ||
                 (data.categories && data.categories.length > 0) ||
                 data.price_min ||
                 data.price_max ||
                 (data.promotions && data.promotions.length > 0));

        console.log('🔍 hasActiveFilters result:', result, 'for data:', data.keyword ? `keyword:"${data.keyword}"` : 'no keyword');

        return result;
    }

    hasActiveFiltersExceptKeyword(data) {
        return !!((data.categories && data.categories.length > 0) ||
                 data.price_min ||
                 data.price_max ||
                 (data.promotions && data.promotions.length > 0));
    }

    handleMainSearchSubmit() {
        console.log('🔍 AJAX Filter: handleMainSearchSubmit called');

        // Ngăn chặn multiple clicks khi đang loading
        if (this.isLoading) {
            console.log('AJAX Filter: Already loading, ignoring search submit');
            return;
        }

        // Ẩn search suggestions nếu có
        this.hideSuggestions();

        // Lấy từ khóa tìm kiếm từ input
        const mainSearchInput = document.getElementById('main-search-input');
        if (!mainSearchInput) {
            console.warn('AJAX Filter: Main search input not found');
            return;
        }

        const keyword = mainSearchInput.value.trim();
        console.log('AJAX Filter: Search keyword:', keyword);

        // Nếu không có từ khóa, hiển thị thông báo
        if (!keyword) {
            this.showNotification('Vui lòng nhập từ khóa tìm kiếm!', 'warning');
            mainSearchInput.focus();
            return;
        }

        // Hiển thị loading state cho nút tìm kiếm
        this.showSearchButtonLoadingState();

        // Tạo filter data với keyword mới và preserve các filter hiện tại (additive filtering)
        // Đây là cách tiếp cận đúng theo UX chuẩn e-commerce: search trong context hiện tại
        const currentFilterData = this.collectFilterData(true); // ignore URL keyword
        const searchData = {
            ...currentFilterData, // Giữ nguyên tất cả filters hiện tại (categories, price, promotions)
            keyword: keyword // Chỉ cập nhật keyword mới
        };

        console.log('AJAX Filter: Loading products with search data (preserving current filters):', searchData);
        this.loadProducts(searchData, 1);

        // KHÔNG clear sidebar filters - giữ nguyên để maintain context

        // Đồng bộ keyword với sidebar search input nếu có
        this.syncSearchInputs(keyword);
    }

    handleFilterSubmit() {
        console.log('AJAX Filter: handleFilterSubmit called');

        // Ngăn chặn multiple clicks khi đang loading
        if (this.isLoading) {
            console.log('AJAX Filter: Already loading, ignoring click');
            return;
        }

        const filterData = this.collectFilterData();
        console.log('AJAX Filter: collected data', filterData);

        // Kiểm tra xem có filter nào được chọn không
        if (!this.hasActiveFilters(filterData)) {
            console.log('AJAX Filter: No active filters');
            this.showNotification('Vui lòng chọn ít nhất một bộ lọc trước khi áp dụng!', 'warning');
            return;
        }

        console.log('AJAX Filter: Loading products...');
        this.loadProducts(filterData, 1);

        // Đồng bộ search inputs để đảm bảo consistency
        this.syncSearchInputs(filterData.keyword || '');
    }

    handleFilterSubmitWithoutKeyword() {
        console.log('AJAX Filter: handleFilterSubmitWithoutKeyword called');

        const filterData = this.collectFilterData();
        console.log('AJAX Filter: collected data without keyword', filterData);

        // Xóa keyword khỏi filterData
        delete filterData.keyword;

        // Kiểm tra xem có filter nào khác được chọn không
        if (!this.hasActiveFilters(filterData)) {
            console.log('AJAX Filter: No active filters after removing keyword, loading all products with AJAX');
            // Thay vì redirect, sử dụng AJAX để load tất cả sản phẩm
            this.loadAllProducts();
            return;
        }

        console.log('AJAX Filter: Loading products without keyword...');
        this.loadProducts(filterData, 1);
    }

    handleRemoveFilter() {
        console.log('🗑️ AJAX Filter: handleRemoveFilter called');

        // Đợi một chút để UI được cập nhật trước khi collect data
        setTimeout(() => {
            // Ignore URL keyword khi remove filter để tránh lấy keyword từ URL cũ
            const filterData = this.collectFilterData(true);
            console.log('🔍 AJAX Filter: collected data after filter removal:', filterData);

            // Kiểm tra xem có filter nào được chọn không
            const hasActiveFilters = this.hasActiveFilters(filterData);

            if (!hasActiveFilters) {
                console.log('📭 AJAX Filter: No active filters after removal, loading all products');
                // Thay vì redirect, sử dụng AJAX để load tất cả sản phẩm
                this.loadAllProducts();
                return;
            }

            console.log('🔄 AJAX Filter: Loading products after filter removal...');
            this.loadProducts(filterData, 1);

            // Đồng bộ search inputs để đảm bảo consistency
            this.syncSearchInputs(filterData.keyword || '');
        }, 50); // Delay ngắn để đảm bảo UI đã được clear
    }

    loadAllProducts() {
        console.log('📭 AJAX Filter: Loading all products');

        // Lấy items_per_page hiện tại từ select
        const itemsPerPageSelect = document.getElementById('items-per-page');
        const currentItemsPerPage = itemsPerPageSelect ? parseInt(itemsPerPageSelect.value) : 12;

        // Tạo empty filter data nhưng giữ nguyên items_per_page
        const emptyFilterData = {
            categories: [],
            promotions: [],
            sort: 'newest',
            items_per_page: currentItemsPerPage
        };

        console.log('📭 AJAX Filter: Loading all products with empty filters');

        // Load products với empty filters
        this.loadProducts(emptyFilterData, 1);

        // Cập nhật URL về trang products.php sạch
        const cleanUrl = window.location.origin + window.location.pathname;
        window.history.pushState({}, '', cleanUrl);

        console.log('📭 AJAX Filter: URL updated to clean products page');
    }

    handleResetFilters() {
        console.log('AJAX Filter: Resetting all filters');

        // Clear tất cả UI elements
        this.clearAllUIElements();

        // Load tất cả sản phẩm với AJAX
        this.loadAllProducts();

        console.log('AJAX Filter: Reset filters completed');
    }

    // Show loading state cho nút reset nhỏ
    showResetLoadingState(resetBtn) {
        if (!resetBtn) return;

        console.log('AJAX Filter: Showing reset loading state');

        // Disable nút và thêm class loading
        resetBtn.disabled = true;
        resetBtn.classList.add('loading');

        // Lấy các elements
        const btnSpinner = resetBtn.querySelector('.btn-spinner');
        const btnResetIcon = resetBtn.querySelector('.btn-reset-icon');
        const btnText = resetBtn.querySelector('.btn-text');
        const btnArrowContainer = resetBtn.querySelector('.btn-arrow-container');

        // Ẩn icon và text cũ
        if (btnResetIcon) {
            btnResetIcon.style.display = 'none';
        }

        if (btnText) {
            btnText.style.display = 'none';
        }

        if (btnArrowContainer) {
            btnArrowContainer.style.display = 'none';
        }

        // Hiện spinner ngay lập tức
        if (btnSpinner) {
            btnSpinner.innerHTML = '<i class="fas fa-spinner fa-spin text-orange-500"></i>';
            btnSpinner.style.display = 'inline-flex';
            btnSpinner.style.alignItems = 'center';
            btnSpinner.style.opacity = '1';
            btnSpinner.style.transform = 'scale(1)';
            btnSpinner.style.transition = 'none'; // Loại bỏ transition để hiển thị ngay lập tức
        }
    }

    // Hide loading state cho nút reset
    hideResetLoadingState(resetBtn) {
        if (!resetBtn) return;

        console.log('AJAX Filter: Hiding reset loading state');

        // Enable nút và remove class loading
        resetBtn.disabled = false;
        resetBtn.classList.remove('loading');

        // Lấy các elements
        const btnSpinner = resetBtn.querySelector('.btn-spinner');
        const btnResetIcon = resetBtn.querySelector('.btn-reset-icon');
        const btnText = resetBtn.querySelector('.btn-text');
        const btnArrowContainer = resetBtn.querySelector('.btn-arrow-container');

        // Ẩn spinner
        if (btnSpinner) {
            btnSpinner.style.display = 'none';
            btnSpinner.innerHTML = '';
        }

        // Hiện lại icon và text
        if (btnResetIcon) {
            btnResetIcon.style.display = '';
        }

        if (btnText) {
            btnText.style.display = '';
        }

        if (btnArrowContainer) {
            btnArrowContainer.style.display = '';
        }
    }

    clearSidebarFilters() {
        console.log('AJAX Filter: Clearing sidebar filters only');

        // Clear tất cả checkboxes trong sidebar
        document.querySelectorAll('input[name="category[]"]').forEach(checkbox => {
            checkbox.checked = false;
        });

        document.querySelectorAll('input[name="promotion[]"]').forEach(checkbox => {
            checkbox.checked = false;
        });

        // Clear price inputs
        const priceMinInput = document.getElementById('price-min');
        const priceMaxInput = document.getElementById('price-max');

        if (priceMinInput) {
            priceMinInput.value = '';
        }
        if (priceMaxInput) {
            priceMaxInput.value = '';
        }

        // Clear active state từ tất cả price preset buttons
        document.querySelectorAll('.price-preset').forEach(btn => {
            btn.className = btn.className.replace(/bg-gradient-to-r|from-orange-500|to-orange-600|text-white|border-orange-500|shadow-md/g, '');
            btn.classList.add('bg-white', 'text-gray-700', 'border-gray-200');
        });

        console.log('AJAX Filter: Sidebar filters cleared');
    }

    syncSearchInputs(keyword = '') {
        console.log('AJAX Filter: Syncing search inputs with keyword:', keyword);

        const mainSearchInput = document.getElementById('main-search-input');
        const sidebarSearchInput = document.querySelector('input[name="keyword"], #search-input');

        // Đồng bộ main search input
        if (mainSearchInput && mainSearchInput.value.trim() !== keyword) {
            mainSearchInput.value = keyword;
        }

        // Đồng bộ sidebar search input (nếu khác với main search)
        if (sidebarSearchInput && sidebarSearchInput !== mainSearchInput && sidebarSearchInput.value.trim() !== keyword) {
            sidebarSearchInput.value = keyword;
        }

        console.log('AJAX Filter: Search inputs synced');
    }

    hideSuggestions() {
        console.log('🔍 AJAX Filter: Hiding search suggestions');

        // Tìm và ẩn suggestions container từ enhanced-search.js
        const suggestionsContainer = document.getElementById('search-suggestions');
        if (suggestionsContainer) {
            suggestionsContainer.classList.add('hidden');
            console.log('✅ Search suggestions hidden');
        }

        // Tìm và ẩn suggestions container từ search.js (nếu có)
        const searchSuggestions = document.querySelectorAll('.search-suggestions');
        searchSuggestions.forEach(container => {
            container.classList.add('hidden');
        });

        // Reset selected index nếu có
        if (window.selectedIndex !== undefined) {
            window.selectedIndex = -1;
        }
    }

    showSearchButtonLoadingState() {
        console.log('🔍 AJAX Filter: Showing search button loading state');

        const searchBtn = document.getElementById('search-submit-btn');
        if (!searchBtn) return;

        // Clear tất cả animations trước khi bắt đầu
        this.clearSearchButtonAnimations(searchBtn);

        // Disable nút và thêm class loading
        searchBtn.disabled = true;
        searchBtn.classList.add('loading');
        searchBtn.classList.remove('success');

        // Lấy tất cả các elements trong nút
        const btnSpinner = searchBtn.querySelector('.btn-spinner');
        const btnSuccess = searchBtn.querySelector('.btn-success');
        const btnSearchIcon = searchBtn.querySelector('.btn-search-icon');
        const btnText = searchBtn.querySelector('.btn-text');

        // Ẩn success icon và search icon
        if (btnSuccess) {
            btnSuccess.style.display = 'none';
            btnSuccess.style.opacity = '0';
            btnSuccess.style.transform = 'scale(0)';
        }

        if (btnSearchIcon) {
            btnSearchIcon.style.transition = 'all 0.2s ease-out';
            btnSearchIcon.style.opacity = '0';
            btnSearchIcon.style.transform = 'scale(0.8)';

            setTimeout(() => {
                btnSearchIcon.style.display = 'none';
            }, 200);
        }

        // Hiển thị spinner với smooth animation
        if (btnSpinner) {
            btnSpinner.style.display = 'flex';
            btnSpinner.innerHTML = `
                <div style="
                    width: 16px;
                    height: 16px;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-top: 2px solid white;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                "></div>
            `;
            btnSpinner.style.opacity = '0';
            btnSpinner.style.transform = 'scale(0.8)';
            btnSpinner.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

            // Delay để tránh conflict với search icon animation
            setTimeout(() => {
                btnSpinner.style.opacity = '1';
                btnSpinner.style.transform = 'scale(1)';
            }, 150);
        }

        // Thay đổi text với smooth transition
        if (btnText) {
            btnText.style.transition = 'all 0.2s ease-out';
            btnText.style.opacity = '0';

            setTimeout(() => {
                btnText.textContent = 'Đang tìm kiếm...';
                btnText.style.transition = 'all 0.3s ease-in';
                btnText.style.opacity = '1';
            }, 200);
        }
    }

    hideSearchButtonLoadingState() {
        console.log('🔍 AJAX Filter: Hiding search button loading state');

        const searchBtn = document.getElementById('search-submit-btn');
        if (!searchBtn) return;

        // Clear tất cả animations trước khi chuyển state
        this.clearSearchButtonAnimations(searchBtn);

        // Lấy tất cả các elements trong nút
        const btnSpinner = searchBtn.querySelector('.btn-spinner');
        const btnSuccess = searchBtn.querySelector('.btn-success');
        const btnText = searchBtn.querySelector('.btn-text');

        // Ẩn spinner ngay lập tức
        if (btnSpinner) {
            btnSpinner.style.opacity = '0';
            btnSpinner.style.transform = 'scale(0)';
            btnSpinner.style.display = 'none';
            btnSpinner.innerHTML = '';
        }

        // Hiển thị success state
        this.showSearchButtonSuccessState();
    }

    showSearchButtonSuccessState() {
        console.log('🔍 AJAX Filter: Showing search button success state');

        const searchBtn = document.getElementById('search-submit-btn');
        if (!searchBtn) return;

        // Thêm success class
        searchBtn.classList.remove('loading');
        searchBtn.classList.add('success');

        // Lấy elements
        const btnSuccess = searchBtn.querySelector('.btn-success');
        const btnText = searchBtn.querySelector('.btn-text');

        // Hiển thị success icon
        if (btnSuccess) {
            btnSuccess.style.display = 'flex';
            btnSuccess.style.opacity = '0';
            btnSuccess.style.transform = 'scale(0)';

            setTimeout(() => {
                btnSuccess.style.transition = 'all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
                btnSuccess.style.opacity = '1';
                btnSuccess.style.transform = 'scale(1)';
            }, 100);
        }

        // Đổi text thành "Tìm kiếm thành công!"
        if (btnText) {
            btnText.style.opacity = '0';

            setTimeout(() => {
                btnText.textContent = 'Tìm kiếm thành công!';
                btnText.style.transition = 'opacity 0.3s ease';
                btnText.style.opacity = '1';
            }, 100);
        }

        // Tự động reset về trạng thái ban đầu sau 2 giây
        setTimeout(() => {
            this.resetSearchButtonToOriginalState();
        }, 2000);
    }

    resetSearchButtonToOriginalState() {
        console.log('🔍 AJAX Filter: Resetting search button to original state');

        const searchBtn = document.getElementById('search-submit-btn');
        if (!searchBtn) return;

        // Clear animations
        this.clearSearchButtonAnimations(searchBtn);

        // Lấy elements
        const btnSpinner = searchBtn.querySelector('.btn-spinner');
        const btnSuccess = searchBtn.querySelector('.btn-success');
        const btnSearchIcon = searchBtn.querySelector('.btn-search-icon');
        const btnText = searchBtn.querySelector('.btn-text');

        // Enable nút và remove classes
        searchBtn.disabled = false;
        searchBtn.classList.remove('loading', 'success');

        // Ẩn success icon và spinner
        if (btnSuccess) {
            btnSuccess.style.opacity = '0';
            btnSuccess.style.transform = 'scale(0)';
            btnSuccess.style.display = 'none';
        }

        if (btnSpinner) {
            btnSpinner.style.opacity = '0';
            btnSpinner.style.transform = 'scale(0)';
            btnSpinner.style.display = 'none';
        }

        // Hiển thị lại search icon
        if (btnSearchIcon) {
            btnSearchIcon.style.display = 'inline-block';
            btnSearchIcon.style.transition = 'all 0.3s ease';

            setTimeout(() => {
                btnSearchIcon.style.opacity = '1';
                btnSearchIcon.style.transform = 'scale(1)';
            }, 100);
        }

        // Reset text
        if (btnText) {
            btnText.style.opacity = '0';

            setTimeout(() => {
                btnText.textContent = 'Tìm kiếm';
                btnText.style.transition = 'opacity 0.3s ease';
                btnText.style.opacity = '1';
            }, 100);
        }
    }

    clearSearchButtonAnimations(searchBtn) {
        if (!searchBtn) return;

        // Clear tất cả inline styles và animations
        const elements = searchBtn.querySelectorAll('*');
        elements.forEach(el => {
            el.style.animation = 'none';
            el.style.transition = 'none';
            // Reset transform và opacity để tránh layout jump
            el.style.transform = '';
            el.style.opacity = '';
        });

        // Clear button's own styles
        searchBtn.style.animation = 'none';
        searchBtn.style.transition = 'none';

        // Force reflow để đảm bảo changes được apply
        searchBtn.offsetHeight;

        // Re-enable transitions sau khi clear
        setTimeout(() => {
            elements.forEach(el => {
                el.style.transition = '';
            });
            searchBtn.style.transition = '';
        }, 10);
    }

    clearAllUIElements() {
        console.log('AJAX Filter: Clearing all UI elements');

        // Clear sidebar filters
        this.clearSidebarFilters();

        // Clear keyword input (cả main search và sidebar search nếu có)
        const mainSearchInput = document.getElementById('main-search-input');
        if (mainSearchInput) {
            mainSearchInput.value = '';
        }

        const sidebarSearchInput = document.querySelector('input[name="keyword"], #search-input');
        if (sidebarSearchInput && sidebarSearchInput !== mainSearchInput) {
            sidebarSearchInput.value = '';
        }

        // Reset sort về mặc định nếu có
        const sortSelect = document.querySelector('select[name="sort"]');
        if (sortSelect) {
            sortSelect.value = 'newest';
        }

        console.log('AJAX Filter: All UI elements cleared');
    }

    async loadProducts(filterData, page = 1, updateHistory = true) {
        console.log('AJAX Filter: loadProducts called', { filterData, page, updateHistory });

        if (this.isLoading) {
            console.log('AJAX Filter: Already loading, skipping');
            return;
        }

        this.isLoading = true;
        this.currentPage = page;

        // Ghi nhận thời gian bắt đầu để đảm bảo loading tối thiểu
        const startTime = Date.now();
        const minLoadingTime = 800; // 800ms tối thiểu

        try {
            // Hiển thị loading state
            this.showLoadingState();

            // Chuẩn bị data để gửi
            const requestData = {
                ...filterData,
                page: page
            };

            console.log('AJAX Filter: Sending request to', this.apiUrl, 'with data', requestData);

            // Gửi AJAX request
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            console.log('AJAX Filter: Response received', response.status, response.ok);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('AJAX Filter: HTTP error', response.status, errorText);
                throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
            }

            const result = await response.json();
            console.log('AJAX Filter: API response', result);

            if (result.success) {
                // Tính toán thời gian đã trôi qua
                const elapsedTime = Date.now() - startTime;
                const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

                console.log(`AJAX Filter: Processing completed in ${elapsedTime}ms, waiting additional ${remainingTime}ms`);

                // Đợi thời gian còn lại để đảm bảo UX tốt
                await new Promise(resolve => setTimeout(resolve, remainingTime));

                // Cập nhật nội dung trang - Filter Results Header trước, sau đó mới đến sản phẩm
                this.updateFilterResultsHeader(result.data);
                this.updateProductsGrid(result.data);
                this.updatePagination(result.data.pagination);
                this.updateProductsStats(result.data.pagination);
                this.updateFilterBadge(result.data.filters);

                // Đồng bộ UI sidebar với filter state hiện tại
                this.syncSidebarUI();

                // Cập nhật URL và history
                if (updateHistory) {
                    this.updateUrlAndHistory(filterData, page);

                    // Đồng bộ UI với URL state mới (nếu có FilterStateManager)
                    if (typeof window.filterStateManager !== 'undefined') {
                        setTimeout(() => {
                            window.filterStateManager.syncUIWithURL();
                            console.log('AJAX Filter: UI synced with URL state');
                        }, 100);
                    }
                }

                // Scroll to products grid sau khi cập nhật xong
                setTimeout(() => {
                    this.scrollToProductsGrid();
                }, 100);

                // Không hiển thị thông báo - đã xóa theo yêu cầu
                // this.showSuccessNotification(result.data);

            } else {
                throw new Error(result.error || 'Có lỗi xảy ra khi lọc sản phẩm');
            }

        } catch (error) {
            console.error('AJAX Filter Error:', error);

            // Đảm bảo thời gian loading tối thiểu ngay cả khi có lỗi
            const elapsedTime = Date.now() - startTime;
            const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

            if (remainingTime > 0) {
                await new Promise(resolve => setTimeout(resolve, remainingTime));
            }

            this.showNotification('Có lỗi xảy ra. Vui lòng thử lại!', 'error');
        } finally {
            this.isLoading = false;
            this.hideLoadingState();

            // Ẩn loading state của search button nếu có
            const searchBtn = document.getElementById('search-submit-btn');
            if (searchBtn && searchBtn.classList.contains('loading')) {
                this.hideSearchButtonLoadingState();
            }
        }
    }

    // Helper function để clear tất cả animation states ngay lập tức
    clearAllButtonAnimations(applyBtn) {
        const elements = applyBtn.querySelectorAll('.btn-spinner, .btn-success, .btn-filter-icon, .btn-text, .ml-2');
        elements.forEach(el => {
            if (el) {
                el.style.transition = 'none';
                el.style.animation = 'none';
                // Force reflow để đảm bảo styles được apply ngay lập tức
                el.offsetHeight;
            }
        });
    }

    showLoadingState() {
        console.log('AJAX Filter: Showing sophisticated loading state');

        const applyBtn = document.getElementById('applyFilters');
        if (!applyBtn) return;

        // Clear tất cả animations trước khi bắt đầu
        this.clearAllButtonAnimations(applyBtn);

        // Disable nút và thêm class loading
        applyBtn.disabled = true;
        applyBtn.classList.add('loading');
        // Đảm bảo remove success class nếu có
        applyBtn.classList.remove('success');

        // Lấy tất cả các elements trong nút
        const btnSpinner = applyBtn.querySelector('.btn-spinner');
        const btnSuccess = applyBtn.querySelector('.btn-success');
        const btnFilterIcon = applyBtn.querySelector('.btn-filter-icon');
        const btnText = applyBtn.querySelector('.btn-text');
        const btnArrowContainer = applyBtn.querySelector('.ml-2');

        // Bước 1: ẨN NGAY LẬP TỨC tất cả elements cũ - KHÔNG có animation
        if (btnSuccess) {
            btnSuccess.style.transition = 'none';
            btnSuccess.style.animation = 'none';
            btnSuccess.style.display = 'none';
        }

        if (btnArrowContainer) {
            btnArrowContainer.style.transition = 'none';
            btnArrowContainer.style.display = 'none';
        }

        if (btnFilterIcon) {
            btnFilterIcon.style.transition = 'none';
            btnFilterIcon.style.display = 'none';
        }

        // Bước 2: Hiện spinner NGAY LẬP TỨC
        if (btnSpinner) {
            // Reset mọi style trước đó
            btnSpinner.style.transition = 'none';
            btnSpinner.style.animation = 'none';
            btnSpinner.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>';
            btnSpinner.style.display = 'inline-flex';
            btnSpinner.style.alignItems = 'center';
            btnSpinner.style.opacity = '0';
            btnSpinner.style.transform = 'scale(0.8)';

            // Force reflow
            btnSpinner.offsetHeight;

            // Bật lại transition cho smooth animation
            btnSpinner.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

            // Fade in spinner
            setTimeout(() => {
                btnSpinner.style.opacity = '1';
                btnSpinner.style.transform = 'scale(1)';
            }, 50);
        }

        // Đổi text NGAY LẬP TỨC
        if (btnText) {
            // Ẩn ngay text cũ
            btnText.style.transition = 'none';
            btnText.style.animation = 'none';
            btnText.style.opacity = '0';

            // Force reflow
            btnText.offsetHeight;

            // Đổi text và hiện ngay
            btnText.textContent = 'Đang lọc sản phẩm...';
            btnText.style.transition = 'opacity 0.3s ease';

            setTimeout(() => {
                btnText.style.opacity = '1';
            }, 50);
        }

        // Products grid overlay
        this.showProductsGridOverlay();
    }

    showProductsGridOverlay() {
        const productsGrid = document.getElementById('productsGrid');
        if (!productsGrid) return;

        let overlay = productsGrid.querySelector('.ajax-loading-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.className = 'ajax-loading-overlay';
            overlay.innerHTML = `
                <div style="text-align: center; padding: 2rem;">
                    <div style="margin-bottom: 1rem;">
                        <i class="fas fa-search text-2xl text-orange-500 mb-2" style="animation: pulse 1.5s ease-in-out infinite;"></i>
                    </div>
                    <div class="text-gray-700 font-medium">Đang tìm kiếm sản phẩm...</div>
                    <div class="text-gray-500 text-sm mt-1">Vui lòng chờ trong giây lát</div>
                </div>
            `;
            overlay.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(1px);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            if (getComputedStyle(productsGrid).position === 'static') {
                productsGrid.style.position = 'relative';
            }

            productsGrid.appendChild(overlay);

            // Fade in overlay
            setTimeout(() => {
                overlay.style.opacity = '1';
            }, 50);
        }
    }

    hideLoadingState() {
        console.log('AJAX Filter: Hiding sophisticated loading state');

        const applyBtn = document.getElementById('applyFilters');
        if (!applyBtn) return;

        // Clear tất cả animations trước khi chuyển state
        this.clearAllButtonAnimations(applyBtn);

        // Lấy tất cả các elements trong nút
        const btnSpinner = applyBtn.querySelector('.btn-spinner');
        const btnSuccess = applyBtn.querySelector('.btn-success');
        const btnText = applyBtn.querySelector('.btn-text');

        // Bước 1: ẨN NGAY LẬP TỨC spinner và loading state - KHÔNG có animation fade out
        if (btnSpinner) {
            // Ẩn ngay lập tức và clear nội dung
            btnSpinner.style.opacity = '0';
            btnSpinner.style.transform = 'scale(0)';
            btnSpinner.style.display = 'none';
            btnSpinner.innerHTML = '';
        }

        // Thêm success class cho nút để trigger CSS animations
        applyBtn.classList.add('success');

        // Hiện success icon với animation NGAY LẬP TỨC - không dùng scale để tránh jump
        if (btnSuccess) {
            // Reset mọi style trước đó
            btnSuccess.style.transition = 'none';
            btnSuccess.style.animation = 'none';
            btnSuccess.style.display = 'inline-flex';
            btnSuccess.style.alignItems = 'center';
            btnSuccess.style.marginRight = '8px';
            btnSuccess.style.opacity = '0';
            // Không dùng scale transform để tránh jump
            btnSuccess.style.transform = '';

            // Force reflow để đảm bảo styles được apply
            btnSuccess.offsetHeight;

            // Bật lại transition cho success state - chỉ opacity
            btnSuccess.style.transition = 'opacity 0.3s ease';
            btnSuccess.style.animation = 'successCheckmark 0.5s cubic-bezier(0.4, 0, 0.2, 1)'; // Easing mượt hơn

            // Hiện success icon ngay lập tức
            setTimeout(() => {
                btnSuccess.style.opacity = '1';
            }, 50); // Delay rất ngắn để animation mượt
        }

        // Success text với animation - ẨN NGAY text cũ, hiện text mới
        if (btnText) {
            // Ẩn ngay lập tức text cũ
            btnText.style.transition = 'none';
            btnText.style.animation = 'none';
            btnText.style.opacity = '0';

            // Force reflow
            btnText.offsetHeight;

            // Đổi text và hiện ngay
            btnText.textContent = 'Lọc thành công!';
            btnText.style.transition = 'opacity 0.3s ease';
            btnText.style.animation = 'successTextSlide 0.4s ease-out';
            btnText.style.fontWeight = '600';
            btnText.style.color = 'white';

            // Hiện text mới
            setTimeout(() => {
                btnText.style.opacity = '1';
            }, 50);
        }

        // Bước 2: Sau 1.2s, reset về trạng thái ban đầu
        setTimeout(() => {
            this.resetButtonToOriginalState(applyBtn);
        }, 1200);

        // Loại bỏ products grid overlay
        this.hideProductsGridOverlay();
    }

    resetButtonToOriginalState(applyBtn) {
        // Clear tất cả animations trước khi reset
        this.clearAllButtonAnimations(applyBtn);

        const btnSpinner = applyBtn.querySelector('.btn-spinner');
        const btnSuccess = applyBtn.querySelector('.btn-success');
        const btnFilterIcon = applyBtn.querySelector('.btn-filter-icon');
        const btnText = applyBtn.querySelector('.btn-text');
        const btnArrowContainer = applyBtn.querySelector('.ml-2');

        // Enable nút và remove loading + success classes
        applyBtn.disabled = false;
        applyBtn.classList.remove('loading', 'success');

        // QUAN TRỌNG: Đảm bảo không có transform scale để tránh jump
        applyBtn.style.transform = '';

        // ẨN NGAY LẬP TỨC success icon - KHÔNG có animation fade out
        if (btnSuccess) {
            btnSuccess.style.opacity = '0';
            btnSuccess.style.transform = 'scale(0)';
            btnSuccess.style.display = 'none';
        }

        // Đảm bảo spinner ẩn hoàn toàn và clean
        if (btnSpinner) {
            btnSpinner.style.display = 'none';
            btnSpinner.innerHTML = '';
        }

        // Hiện lại filter icon NGAY LẬP TỨC - không dùng scale để tránh jump
        if (btnFilterIcon) {
            // Reset mọi style
            btnFilterIcon.style.transition = 'none';
            btnFilterIcon.style.animation = 'none';
            btnFilterIcon.style.display = 'inline-block';
            btnFilterIcon.style.opacity = '0';
            // Không dùng scale transform để tránh jump
            btnFilterIcon.style.transform = '';

            // Force reflow
            btnFilterIcon.offsetHeight;

            // Bật lại transition cho smooth animation - chỉ opacity
            btnFilterIcon.style.transition = 'opacity 0.3s ease';

            // Hiện icon ngay
            setTimeout(() => {
                btnFilterIcon.style.opacity = '1';
            }, 50);
        }

        // Reset text NGAY LẬP TỨC
        if (btnText) {
            // Ẩn ngay text cũ
            btnText.style.transition = 'none';
            btnText.style.animation = 'none';
            btnText.style.opacity = '0';

            // Force reflow
            btnText.offsetHeight;

            // Đổi text và style ngay
            btnText.textContent = 'Áp dụng bộ lọc';
            btnText.style.fontWeight = '500'; // Reset font weight
            btnText.style.color = ''; // Reset color
            btnText.style.transition = 'opacity 0.3s ease';

            // Hiện text mới
            setTimeout(() => {
                btnText.style.opacity = '1';
            }, 50);
        }

        // Hiện lại arrow container NGAY LẬP TỨC - không dùng transform
        if (btnArrowContainer) {
            btnArrowContainer.style.transition = 'none';
            btnArrowContainer.style.animation = 'none';
            btnArrowContainer.style.display = 'block';
            btnArrowContainer.style.opacity = '0'; // Ẩn, chỉ hiện khi hover
            // Không dùng transform để tránh jump
            btnArrowContainer.style.transform = '';

            // Force reflow
            btnArrowContainer.offsetHeight;

            // Bật lại transition - chỉ opacity
            btnArrowContainer.style.transition = 'opacity 0.3s ease';
        }
    }

    hideProductsGridOverlay() {
        const productsGrid = document.getElementById('productsGrid');
        if (!productsGrid) return;

        const overlay = productsGrid.querySelector('.ajax-loading-overlay');
        if (overlay) {
            overlay.style.transition = 'opacity 0.3s ease';
            overlay.style.opacity = '0';

            setTimeout(() => {
                if (overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
            }, 300);
        }
    }

    showNotification(message, type = 'info') {
        // Sử dụng hàm showFilterNotification có sẵn nếu có
        if (typeof showFilterNotification === 'function') {
            showFilterNotification(message, type, false);
        } else {
            // Fallback notification
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }



    scrollToProductsGrid() {
        console.log('AJAX Filter: Auto-scrolling to products section');

        // Tìm products-section thay vì productsGrid để scroll chính xác hơn
        const productsSection = document.getElementById('products-section');
        if (!productsSection) {
            console.warn('AJAX Filter: Products section not found for scrolling');
            return;
        }

        // Tính toán chính xác vị trí scroll (sử dụng logic giống products.php)
        let finalHeaderHeight = 0;

        // Kiểm tra mobile/desktop
        const isMobile = window.innerWidth <= 768;

        if (isMobile) {
            // Mobile: scroll thêm 60px để đến đúng vị trí danh sách sản phẩm
            const mobileHeader = document.querySelector('.mobile-header');
            if (mobileHeader && window.getComputedStyle(mobileHeader).display !== 'none') {
                finalHeaderHeight = -60; // Giá trị âm để cuộn thêm 60px
                console.log('AJAX Filter: Mobile header calculation:', {
                    mobileHeaderHeight: mobileHeader.offsetHeight,
                    finalHeaderHeight: finalHeaderHeight,
                    screenWidth: window.innerWidth,
                    note: 'Scroll additional 60px for mobile'
                });
            } else {
                finalHeaderHeight = -60; // Fallback cho mobile
            }
        } else {
            // Desktop: tính header height sau khi top bar ẩn
            const premiumHeader = document.querySelector('.premium-header');
            if (premiumHeader && window.getComputedStyle(premiumHeader).display !== 'none') {
                const topBar = premiumHeader.querySelector('.top-bar');

                if (topBar) {
                    // Header height = total height - top bar height (vì top bar sẽ ẩn khi scroll)
                    const totalHeight = premiumHeader.offsetHeight;
                    const topBarHeight = topBar.offsetHeight;
                    finalHeaderHeight = totalHeight - topBarHeight;

                    console.log('AJAX Filter: Desktop header calculation:', {
                        totalHeight,
                        topBarHeight,
                        finalHeaderHeight,
                        calculation: `${totalHeight} - ${topBarHeight} = ${finalHeaderHeight}`
                    });
                } else {
                    // Fallback nếu không tìm thấy top bar
                    finalHeaderHeight = premiumHeader.offsetHeight;
                }
            } else {
                // Fallback cho desktop
                const mainHeader = document.querySelector('.main-header, header');
                if (mainHeader) {
                    finalHeaderHeight = mainHeader.offsetHeight;
                }
            }
        }

        // Tính toán vị trí target
        const productsOffset = productsSection.offsetTop;
        const targetPosition = productsOffset - finalHeaderHeight;

        console.log('AJAX Filter: Final scroll calculation:', {
            isMobile,
            productsOffset,
            finalHeaderHeight,
            targetPosition,
            calculation: `${productsOffset} - ${finalHeaderHeight} = ${targetPosition}`
        });

        // Smooth scroll với animation tùy chỉnh
        this.smoothScrollTo(targetPosition, 800);
    }

    smoothScrollTo(targetPosition, duration = 800) {
        const startPosition = window.pageYOffset || document.documentElement.scrollTop;
        const distance = targetPosition - startPosition;
        let startTime = null;

        function animation(currentTime) {
            if (startTime === null) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const progress = Math.min(timeElapsed / duration, 1);

            // Easing function (ease-out-cubic)
            const easeOutCubic = 1 - Math.pow(1 - progress, 3);

            window.scrollTo(0, startPosition + distance * easeOutCubic);

            if (timeElapsed < duration) {
                requestAnimationFrame(animation);
            }
        }

        requestAnimationFrame(animation);
    }

    updateUrlAndHistory(filterData, page) {
        const url = new URL(window.location);

        // Xóa các tham số cũ
        url.searchParams.delete('keyword');
        url.searchParams.delete('category');
        url.searchParams.delete('category[]');
        url.searchParams.delete('price_min');
        url.searchParams.delete('price_max');
        url.searchParams.delete('promotion');
        url.searchParams.delete('promotion[]');
        url.searchParams.delete('sort');
        url.searchParams.delete('page');
        url.searchParams.delete('items_per_page');

        // Thêm các tham số mới
        if (filterData.keyword && filterData.keyword.trim() !== '') {
            url.searchParams.set('keyword', filterData.keyword.trim());
        }

        if (filterData.categories && filterData.categories.length > 0) {
            filterData.categories.forEach(catId => {
                url.searchParams.append('category[]', catId);
            });
        }

        if (filterData.price_min) {
            url.searchParams.set('price_min', filterData.price_min);
        }

        if (filterData.price_max) {
            url.searchParams.set('price_max', filterData.price_max);
        }

        if (filterData.promotions && filterData.promotions.length > 0) {
            filterData.promotions.forEach(promo => {
                url.searchParams.append('promotion[]', promo);
            });
        }

        // Luôn luôn include sort để đảm bảo consistency
        if (filterData.sort) {
            url.searchParams.set('sort', filterData.sort);
        }

        if (page > 1) {
            url.searchParams.set('page', page);
        }

        // Luôn luôn include items_per_page để đảm bảo consistency
        if (filterData.items_per_page) {
            url.searchParams.set('items_per_page', filterData.items_per_page);
        }

        // Cập nhật history
        const state = {
            isAjaxFilter: true,
            filterData: filterData,
            page: page
        };

        history.pushState(state, '', url.toString());
    }

    updateProductsGrid(data) {
        console.log('AJAX Filter: updateProductsGrid called', data);

        const productsGrid = document.getElementById('productsGrid');
        if (!productsGrid) {
            console.error('AJAX Filter: productsGrid element not found');
            return;
        }

        console.log('AJAX Filter: productsGrid element found', productsGrid);

        // Tạo nội dung mới
        let newContent = '';
        if (data.products && data.products.length > 0) {
            console.log('AJAX Filter: Rendering', data.products.length, 'products');
            newContent = data.products.map(product => this.renderProductCard(product)).join('');
        } else {
            console.log('AJAX Filter: No products, rendering empty state');
            newContent = this.renderEmptyState(data.filters);
        }

        // Hiệu ứng chuyển đổi mượt mà
        this.animateProductsTransition(productsGrid, newContent);

        // Nếu là empty state, load popular categories
        if (!data.products || data.products.length === 0) {
            setTimeout(() => {
                this.loadPopularCategories();
            }, 500); // Delay để animation hoàn thành
        }
    }

    animateProductsTransition(productsGrid, newContent) {
        // Lấy tất cả sản phẩm hiện tại (trừ loading overlay)
        const currentProducts = Array.from(productsGrid.children).filter(
            child => !child.classList.contains('ajax-loading-overlay')
        );

        if (currentProducts.length === 0) {
            // Không có sản phẩm cũ, chỉ fade in sản phẩm mới
            productsGrid.innerHTML = newContent;
            this.fadeInNewProducts(productsGrid);
            return;
        }

        // Fade out sản phẩm cũ
        console.log('AJAX Filter: Fading out old products');
        currentProducts.forEach((product, index) => {
            product.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            product.style.opacity = '0';
            product.style.transform = 'translateY(-10px)';
        });

        // Sau khi fade out xong, thay thế nội dung và fade in
        setTimeout(() => {
            productsGrid.innerHTML = newContent;
            this.fadeInNewProducts(productsGrid);
            console.log('AJAX Filter: Products transition completed');
        }, 300);
    }

    fadeInNewProducts(productsGrid) {
        const newProducts = Array.from(productsGrid.children);

        // Đặt opacity ban đầu là 0 và transform
        newProducts.forEach(product => {
            product.style.opacity = '0';
            product.style.transform = 'translateY(20px)';
            product.style.transition = 'opacity 0.4s ease, transform 0.4s ease';
        });

        // Fade in từng sản phẩm với delay staggered
        newProducts.forEach((product, index) => {
            setTimeout(() => {
                product.style.opacity = '1';
                product.style.transform = 'translateY(0)';
            }, index * 50); // Delay 50ms cho mỗi sản phẩm
        });

        // Reset styles sau khi animation hoàn thành
        setTimeout(() => {
            newProducts.forEach(product => {
                product.style.transition = '';
                product.style.transform = '';
            });
        }, 400 + (newProducts.length * 50));
    }

    renderProductCard(product) {
        const discountPercent = product.sale_price > 0 && product.price > product.sale_price
            ? Math.round(((product.price - product.sale_price) / product.price) * 100)
            : 0;

        const saleBadge = discountPercent > 0 ? `
            <div class="premium-sale-badge">
                <div class="badge-content">
                    <span class="discount-percent">-${discountPercent}%</span>
                    <span class="sale-text">SALE</span>
                </div>
            </div>
        ` : '';

        const priceSection = this.renderPriceSection(product);
        const stars = this.renderStars(product.rating || 5);

        return `
            <div class="group h-full flex flex-col bg-white rounded-2xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-500 border border-gray-100 hover:border-blue-200 hover:-translate-y-2">
                <div class="product-image-wrapper relative">
                    <a href="${product.url}" class="block product-image">
                        ${product.image ?
                            `<img src="${window.BASE_URL}/uploads/products/${product.image}" alt="${product.name}" class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">` :
                            `<div class="w-full h-full bg-gray-300 flex items-center justify-center absolute top-0 left-0">
                                <i class="fas fa-image text-gray-500 text-4xl"></i>
                            </div>`
                        }
                    </a>
                    ${saleBadge}
                </div>
                <div class="product-info-wrapper flex flex-col flex-grow">
                    <div class="product-title mb-3">
                        <a href="${product.url}" class="block">
                            <h3 class="text-lg font-semibold text-gray-800 hover:text-blue-500 transition duration-200 line-clamp-2 leading-tight">
                                ${product.name}
                            </h3>
                        </a>
                    </div>
                    ${priceSection}
                    <div class="product-rating-sales">
                        <div class="rating-section">
                            <div class="stars">${stars}</div>
                            <span class="rating-text">${parseFloat(product.rating || 5).toFixed(1)}</span>
                        </div>
                        <div class="sales-section">
                            <i class="fas fa-shopping-cart"></i>
                            <span>${(product.sold || 0).toLocaleString()} đã bán</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderPriceSection(product) {
        if (product.price_type === 'contact') {
            return `
                <div class="premium-price-section">
                    <div class="contact-price-container">
                        <div class="contact-price-main">GỌI NGAY</div>
                        <div class="contact-price-subtitle">Liên hệ báo giá</div>
                    </div>
                </div>
            `;
        } else if (product.sale_price > 0) {
            return `
                <div class="premium-price-section">
                    <div class="price-container">
                        <div class="original-price">${this.formatCurrency(product.price)}</div>
                        <div class="sale-price">${this.formatCurrency(product.sale_price)}</div>
                    </div>
                </div>
            `;
        } else {
            return `
                <div class="premium-price-section">
                    <div class="regular-price-container">
                        <div class="price-label">Giá bán</div>
                        <div class="main-price">${this.formatCurrency(product.price)}</div>
                    </div>
                </div>
            `;
        }
    }

    renderStars(rating) {
        const numRating = parseFloat(rating) || 5;
        let stars = '';
        for (let i = 1; i <= 5; i++) {
            stars += `<i class="fas fa-star${i <= numRating ? '' : '-o'}"></i>`;
        }
        return stars;
    }

    formatCurrency(amount) {
        // Sử dụng format giống như PHP format_currency function
        if (!amount || isNaN(amount)) return '0 đ';
        return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + ' đ';
    }

    renderEmptyState(filters) {
        const hasKeyword = filters.keyword && filters.keyword.trim() !== '';

        return `
            <div class="col-span-full py-16 text-center">
                <div class="max-w-2xl mx-auto">
                    <!-- Enhanced Empty State -->
                    <div class="bg-gradient-to-br from-orange-50/50 to-amber-50/50 rounded-2xl p-8 border border-orange-100/50 shadow-sm">
                        <!-- Animated Icon -->
                        <div class="relative mb-6">
                            <div class="w-20 h-20 mx-auto bg-gradient-to-br from-orange-100 to-amber-100 rounded-full flex items-center justify-center shadow-lg">
                                ${hasKeyword ?
                                    '<i class="fas fa-search-minus text-orange-400 text-2xl"></i>' :
                                    '<i class="fas fa-box-open text-orange-400 text-2xl"></i>'
                                }
                            </div>
                            <!-- Pulse animation -->
                            <div class="absolute inset-0 w-20 h-20 mx-auto bg-orange-200/30 rounded-full animate-ping"></div>
                        </div>

                        <!-- Main Message -->
                        <h3 class="text-xl font-bold text-gray-800 mb-3">
                            ${hasKeyword ? 'Không tìm thấy sản phẩm phù hợp' : 'Không có sản phẩm trong bộ lọc này'}
                        </h3>

                        <!-- Detailed Description -->
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            ${hasKeyword ?
                                `Không có sản phẩm nào khớp với từ khóa <span class="inline-flex items-center px-2 py-1 bg-orange-100 text-orange-700 rounded font-medium">"${filters.keyword}"</span>` :
                                'Không có sản phẩm nào phù hợp với các tiêu chí lọc hiện tại'
                            }
                        </p>

                        <!-- Helpful Suggestions -->
                        <div class="bg-white/60 rounded-xl p-6 mb-6 border border-orange-100/50">
                            <h4 class="text-sm font-semibold text-gray-700 mb-4 flex items-center">
                                <i class="fas fa-lightbulb text-amber-500 mr-2"></i>
                                Gợi ý để tìm thấy sản phẩm:
                            </h4>
                            <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-3 text-sm text-gray-600">
                                ${hasKeyword ? `
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                        <span>Thử từ khóa ngắn gọn hơn</span>
                                    </div>
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                        <span>Kiểm tra chính tả từ khóa</span>
                                    </div>
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                        <span>Sử dụng từ đồng nghĩa</span>
                                    </div>
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                        <span>Thử bỏ bớt bộ lọc</span>
                                    </div>
                                ` : `
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                        <span>Mở rộng khoảng giá</span>
                                    </div>
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                        <span>Thử danh mục khác</span>
                                    </div>
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                        <span>Bỏ bớt tiêu chí lọc</span>
                                    </div>
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                        <span>Xem tất cả sản phẩm</span>
                                    </div>
                                `}
                            </div>
                        </div>

                        <!-- Popular Categories -->
                        <div class="mb-6">
                            <h4 class="text-sm font-semibold text-gray-700 mb-3">Danh mục phổ biến:</h4>
                            <div class="flex flex-wrap justify-center gap-2" id="popular-categories-ajax">
                                <!-- Categories will be loaded here -->
                            </div>
                        </div>

                        <!-- Contact Support -->
                        <div class="text-center">
                            <p class="text-sm text-gray-500 mb-3">Không tìm thấy sản phẩm bạn cần?</p>
                            <div class="flex flex-col sm:flex-row gap-2 justify-center">
                                <a href="${window.BASE_URL}/contact.php" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg text-sm">
                                    <i class="fas fa-headset mr-2"></i>
                                    Liên hệ tư vấn
                                </a>
                                <a href="${window.BASE_URL}/products.php" class="inline-flex items-center px-4 py-2 bg-white border-2 border-orange-200 hover:border-orange-300 text-orange-600 hover:text-orange-700 font-medium rounded-lg transition-all duration-200 hover:bg-orange-50 text-sm">
                                    <i class="fas fa-th-large mr-2"></i>
                                    Xem tất cả sản phẩm
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    loadPopularCategories() {
        console.log('AJAX Filter: Loading popular categories for empty state');

        const categoriesContainer = document.getElementById('popular-categories-ajax');
        if (!categoriesContainer) {
            console.warn('AJAX Filter: Popular categories container not found');
            return;
        }

        // Show loading state
        categoriesContainer.innerHTML = `
            <div class="flex items-center justify-center py-4">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
                <span class="ml-2 text-sm text-gray-500">Đang tải danh mục...</span>
            </div>
        `;

        // Make AJAX request to get popular categories
        fetch(`${window.BASE_URL}/ajax/get_popular_categories.php`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('AJAX Filter: Popular categories loaded', data);

            if (data.success && data.categories && data.categories.length > 0) {
                const categoriesHtml = data.categories.map(cat => `
                    <a href="${window.BASE_URL}/products.php?category=${cat.id}"
                       class="inline-flex items-center px-3 py-2 bg-white border border-orange-200 hover:border-orange-300 text-orange-600 hover:text-orange-700 rounded-lg text-xs font-medium transition-all duration-200 hover:bg-orange-50 hover:shadow-sm">
                        <i class="fas fa-tag mr-1.5 text-xs"></i>
                        ${cat.name}
                        <span class="ml-1 text-gray-400">(${cat.product_count})</span>
                    </a>
                `).join('');

                categoriesContainer.innerHTML = categoriesHtml;
            } else {
                // Fallback if no categories
                categoriesContainer.innerHTML = `
                    <div class="text-sm text-gray-500">Không có danh mục phổ biến</div>
                `;
            }
        })
        .catch(error => {
            console.error('AJAX Filter: Error loading popular categories:', error);
            // Hide the container on error
            categoriesContainer.innerHTML = `
                <div class="text-sm text-gray-400">Không thể tải danh mục</div>
            `;
        });
    }

    updateFilterResultsHeader(data) {
        console.log('AJAX Filter: updateFilterResultsHeader called', data);

        // Tìm container chứa Filter Results Header
        const productsContent = document.querySelector('.products-content');
        if (!productsContent) {
            console.warn('AJAX Filter: Products content container not found');
            return;
        }

        // Tìm vị trí để chèn/cập nhật Filter Results Header
        // Nó nằm ngay sau phần header của products content và trước products grid
        const existingHeader = productsContent.querySelector('.filter-results-header');
        const productsGrid = document.getElementById('productsGrid');

        if (!productsGrid) {
            console.warn('AJAX Filter: Products grid not found');
            return;
        }

        // Nếu có HTML từ API, sử dụng nó
        if (data.filter_results_header_html && data.filter_results_header_html.trim() !== '') {
            console.log('AJAX Filter: Using HTML from API for filter results header');

            if (existingHeader) {
                // Thay thế header hiện tại bằng animation
                this.replaceFilterResultsHeader(existingHeader, data.filter_results_header_html);
            } else {
                // Tạo mới header và chèn vào vị trí phù hợp
                this.insertFilterResultsHeader(productsGrid, data.filter_results_header_html);
            }
        } else {
            console.log('AJAX Filter: No filter results header HTML from API, hiding existing header');

            // Nếu không có HTML từ API (không có filter), ẩn header hiện tại
            if (existingHeader) {
                this.hideFilterResultsHeader(existingHeader);
            }
        }

        console.log('AJAX Filter: Filter results header updated successfully');
    }

    replaceFilterResultsHeader(existingHeader, newHtml) {
        console.log('AJAX Filter: Replacing existing filter results header');

        // Fade out header cũ
        existingHeader.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        existingHeader.style.opacity = '0';
        existingHeader.style.transform = 'translateY(-10px)';

        setTimeout(() => {
            // Tạo element mới từ HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = newHtml;
            const newHeader = tempDiv.firstElementChild;

            if (newHeader) {
                // Đặt opacity ban đầu là 0 cho animation
                newHeader.style.opacity = '0';
                newHeader.style.transform = 'translateY(10px)';
                newHeader.style.transition = 'opacity 0.4s ease, transform 0.4s ease';

                // Thay thế element
                existingHeader.parentNode.replaceChild(newHeader, existingHeader);

                // Fade in header mới
                setTimeout(() => {
                    newHeader.style.opacity = '1';
                    newHeader.style.transform = 'translateY(0)';
                }, 50);

                // Reset styles sau animation
                setTimeout(() => {
                    newHeader.style.transition = '';
                    newHeader.style.transform = '';
                }, 450);
            }
        }, 300);
    }

    insertFilterResultsHeader(productsGrid, newHtml) {
        console.log('AJAX Filter: Inserting new filter results header');

        // Tạo element mới từ HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = newHtml;
        const newHeader = tempDiv.firstElementChild;

        if (newHeader) {
            // Đặt opacity ban đầu là 0 cho animation
            newHeader.style.opacity = '0';
            newHeader.style.transform = 'translateY(20px)';
            newHeader.style.transition = 'opacity 0.4s ease, transform 0.4s ease';

            // Chèn trước products grid
            productsGrid.parentNode.insertBefore(newHeader, productsGrid);

            // Fade in header mới
            setTimeout(() => {
                newHeader.style.opacity = '1';
                newHeader.style.transform = 'translateY(0)';
            }, 50);

            // Reset styles sau animation
            setTimeout(() => {
                newHeader.style.transition = '';
                newHeader.style.transform = '';
            }, 450);
        }
    }

    hideFilterResultsHeader(existingHeader) {
        console.log('AJAX Filter: Hiding filter results header');

        // Fade out và remove
        existingHeader.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        existingHeader.style.opacity = '0';
        existingHeader.style.transform = 'translateY(-20px)';

        setTimeout(() => {
            if (existingHeader.parentNode) {
                existingHeader.parentNode.removeChild(existingHeader);
            }
        }, 300);
    }

    updateFilterBadge(filters) {
        console.log('AJAX Filter: updateFilterBadge called', filters);

        // Tính số lượng filter active
        let activeFiltersCount = 0;
        let filterDetails = [];

        if (filters.keyword && filters.keyword.trim() !== '') {
            activeFiltersCount++;
            filterDetails.push('từ khóa');
        }

        if (filters.categories && filters.categories.length > 0) {
            activeFiltersCount++;
            filterDetails.push('danh mục');
        }

        if (filters.price_min || filters.price_max) {
            activeFiltersCount++;
            filterDetails.push('khoảng giá');
        }

        if (filters.promotions && filters.promotions.length > 0) {
            activeFiltersCount++;
            filterDetails.push('khuyến mãi');
        }

        console.log('AJAX Filter: Active filters count:', activeFiltersCount);

        // Tìm nút reset filters
        const resetButton = document.getElementById('resetFilters');
        if (!resetButton) {
            console.warn('AJAX Filter: Reset button not found');
            return;
        }

        // Tìm badge hiện tại
        let badge = resetButton.querySelector('.absolute.-top-1.-right-1');

        if (activeFiltersCount > 0) {
            // Có filter active - hiển thị/cập nhật badge
            if (!badge) {
                // Tạo badge mới
                badge = document.createElement('div');
                badge.className = 'absolute -top-1 -right-1 min-w-[18px] h-[18px] bg-orange-500 text-white text-xs font-bold rounded-full flex items-center justify-center animate-pulse';
                resetButton.appendChild(badge);

                // Animation xuất hiện
                badge.style.opacity = '0';
                badge.style.transform = 'scale(0)';
                badge.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

                setTimeout(() => {
                    badge.style.opacity = '1';
                    badge.style.transform = 'scale(1)';
                }, 50);
            }

            // Cập nhật số lượng với animation
            if (badge.textContent !== activeFiltersCount.toString()) {
                badge.style.transform = 'scale(1.2)';
                badge.textContent = activeFiltersCount;

                setTimeout(() => {
                    badge.style.transform = 'scale(1)';
                }, 150);
            }

            // Cập nhật classes của button
            resetButton.className = resetButton.className.replace(
                /text-slate-400 hover:text-orange-500 hover:bg-orange-50 border-transparent hover:border-orange-200/g,
                'text-orange-500 hover:text-orange-600 bg-orange-50 hover:bg-orange-100 border-orange-200 hover:border-orange-300'
            );

            // Cập nhật tooltip
            const tooltipText = `Xóa tất cả bộ lọc (${activeFiltersCount} bộ lọc: ${filterDetails.join(', ')})`;
            resetButton.setAttribute('title', tooltipText);

        } else {
            // Không có filter active - ẩn badge
            if (badge) {
                // Animation biến mất
                badge.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                badge.style.opacity = '0';
                badge.style.transform = 'scale(0)';

                setTimeout(() => {
                    if (badge.parentNode) {
                        badge.parentNode.removeChild(badge);
                    }
                }, 300);
            }

            // Cập nhật classes của button về trạng thái inactive
            resetButton.className = resetButton.className.replace(
                /text-orange-500 hover:text-orange-600 bg-orange-50 hover:bg-orange-100 border-orange-200 hover:border-orange-300/g,
                'text-slate-400 hover:text-orange-500 hover:bg-orange-50 border-transparent hover:border-orange-200'
            );

            // Cập nhật tooltip
            resetButton.setAttribute('title', 'Đặt lại bộ lọc');
        }

        console.log('AJAX Filter: Filter badge updated successfully');
    }

    syncSidebarUI() {
        console.log('AJAX Filter: Syncing sidebar UI with current filter state');

        // Lấy filter data hiện tại
        const filterData = this.collectFilterData();

        // Đồng bộ search inputs
        this.syncSearchInputs(filterData.keyword || '');

        // Đồng bộ price preset buttons
        this.syncPricePresetButtons(filterData.price_min, filterData.price_max);

        // Đồng bộ category checkboxes
        this.syncCategoryCheckboxes(filterData.categories || []);

        // Đồng bộ promotion checkboxes
        this.syncPromotionCheckboxes(filterData.promotions || []);

        // Đồng bộ price inputs
        this.syncPriceInputs(filterData.price_min, filterData.price_max);

        console.log('AJAX Filter: Sidebar UI sync completed');
    }

    syncPricePresetButtons(priceMin, priceMax) {
        console.log('AJAX Filter: Syncing price preset buttons', { priceMin, priceMax });

        // Tìm tất cả price preset buttons
        const pricePresets = document.querySelectorAll('.price-preset');

        pricePresets.forEach(button => {
            const buttonMin = parseInt(button.getAttribute('data-min')) || 0;
            const buttonMax = button.getAttribute('data-max') ? parseInt(button.getAttribute('data-max')) : null;

            // Kiểm tra xem button này có match với filter hiện tại không
            let isActive = false;

            if (buttonMax === null) {
                // "Trên X triệu" case
                isActive = (priceMin == buttonMin && !priceMax);
            } else {
                // Range case
                isActive = (priceMin == buttonMin && priceMax == buttonMax);
            }

            // Cập nhật trạng thái
            if (isActive) {
                // Activate button
                button.className = button.className.replace(/bg-white|text-gray-700|border-gray-200/g, '');
                button.classList.add('from-orange-500', 'bg-white', 'text-gray-700', 'border-gray-200');
            } else {
                // Deactivate button
                button.className = button.className.replace(/bg-gradient-to-r|from-orange-500|to-orange-600|text-white|border-orange-500|shadow-md/g, '');
                button.classList.add('bg-white', 'text-gray-700', 'border-gray-200');
            }
        });

        console.log('AJAX Filter: Price preset buttons synced');
    }

    syncCategoryCheckboxes(activeCategories) {
        console.log('AJAX Filter: Syncing category checkboxes', activeCategories);

        // Tìm tất cả category checkboxes
        const categoryCheckboxes = document.querySelectorAll('input[name="category[]"]');

        categoryCheckboxes.forEach(checkbox => {
            const categoryId = parseInt(checkbox.value);
            checkbox.checked = activeCategories.includes(categoryId);
        });

        console.log('AJAX Filter: Category checkboxes synced');
    }

    syncPromotionCheckboxes(activePromotions) {
        console.log('AJAX Filter: Syncing promotion checkboxes', activePromotions);

        // Tìm tất cả promotion checkboxes
        const promotionCheckboxes = document.querySelectorAll('input[name="promotion[]"]');

        promotionCheckboxes.forEach(checkbox => {
            const promotionValue = checkbox.value;
            checkbox.checked = activePromotions.includes(promotionValue);
        });

        console.log('AJAX Filter: Promotion checkboxes synced');
    }

    syncPriceInputs(priceMin, priceMax) {
        console.log('AJAX Filter: Syncing price inputs', { priceMin, priceMax });

        // Tìm price inputs
        const priceMinInput = document.getElementById('price-min');
        const priceMaxInput = document.getElementById('price-max');

        if (priceMinInput) {
            priceMinInput.value = priceMin || '';
        }

        if (priceMaxInput) {
            priceMaxInput.value = priceMax || '';
        }

        console.log('AJAX Filter: Price inputs synced');
    }

    updatePagination(pagination) {
        // Tìm container pagination
        const paginationContainer = document.querySelector('.pagination-container, .pagination-section');
        if (!paginationContainer) return;

        if (pagination.total_pages <= 1) {
            // Ẩn pagination nếu chỉ có 1 trang
            paginationContainer.style.display = 'none';
            return;
        }

        paginationContainer.style.display = 'block';

        // Render pagination HTML
        const paginationHTML = this.renderPagination(pagination);

        // Tìm phần pagination links để thay thế
        const paginationLinks = paginationContainer.querySelector('.pagination-links, .flex.justify-center');
        if (paginationLinks) {
            paginationLinks.innerHTML = paginationHTML;
        }
    }

    renderPagination(pagination) {
        const { current_page, total_pages, has_prev, has_next } = pagination;
        let html = '';

        // Previous button
        if (has_prev) {
            html += `
                <a href="#" class="ajax-pagination-link px-3 py-2 ml-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100 hover:text-gray-700" data-page="${current_page - 1}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            `;
        } else {
            html += `
                <span class="px-3 py-2 ml-0 leading-tight text-gray-300 bg-gray-100 border border-gray-300 rounded-l-lg cursor-not-allowed">
                    <i class="fas fa-chevron-left"></i>
                </span>
            `;
        }

        // Page numbers
        const startPage = Math.max(1, current_page - 2);
        const endPage = Math.min(total_pages, current_page + 2);

        // First page
        if (startPage > 1) {
            html += `
                <a href="#" class="ajax-pagination-link px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700" data-page="1">1</a>
            `;
            if (startPage > 2) {
                html += `<span class="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300">...</span>`;
            }
        }

        // Page range
        for (let i = startPage; i <= endPage; i++) {
            if (i === current_page) {
                html += `
                    <span class="px-3 py-2 leading-tight text-blue-600 bg-blue-50 border border-gray-300 font-medium">${i}</span>
                `;
            } else {
                html += `
                    <a href="#" class="ajax-pagination-link px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700" data-page="${i}">${i}</a>
                `;
            }
        }

        // Last page
        if (endPage < total_pages) {
            if (endPage < total_pages - 1) {
                html += `<span class="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300">...</span>`;
            }
            html += `
                <a href="#" class="ajax-pagination-link px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700" data-page="${total_pages}">${total_pages}</a>
            `;
        }

        // Next button
        if (has_next) {
            html += `
                <a href="#" class="ajax-pagination-link px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700" data-page="${current_page + 1}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            `;
        } else {
            html += `
                <span class="px-3 py-2 leading-tight text-gray-300 bg-gray-100 border border-gray-300 rounded-r-lg cursor-not-allowed">
                    <i class="fas fa-chevron-right"></i>
                </span>
            `;
        }

        return html;
    }

    bindSortAndPaginationEvents() {
        console.log('AJAX Filter: Binding sort and pagination events');

        // Bind sort select
        const sortSelect = document.getElementById('sort-select');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                console.log('AJAX Filter: Sort changed to', e.target.value);
                this.handleSortChange(e.target.value);
            });
            console.log('AJAX Filter: Sort select bound');
        }

        // Bind items per page select
        const itemsPerPageSelect = document.getElementById('items-per-page');
        if (itemsPerPageSelect) {
            itemsPerPageSelect.addEventListener('change', (e) => {
                console.log('AJAX Filter: Items per page changed to', e.target.value);
                this.handleItemsPerPageChange(e.target.value);
            });
            console.log('AJAX Filter: Items per page select bound');
        }
    }

    handleSortChange(sortValue) {
        console.log('AJAX Filter: handleSortChange called with', sortValue);

        // Collect current filter data
        const filterData = this.collectFilterData();

        // Update sort value
        filterData.sort = sortValue;

        // Load products with new sort (reset to page 1)
        this.loadProducts(filterData, 1);
    }

    handleItemsPerPageChange(itemsPerPage) {
        console.log('AJAX Filter: handleItemsPerPageChange called with', itemsPerPage);

        // Collect current filter data
        const filterData = this.collectFilterData();

        // Update items per page
        filterData.items_per_page = parseInt(itemsPerPage);

        // Load products with new items per page (reset to page 1)
        this.loadProducts(filterData, 1);
    }

    updateProductsStats(pagination) {
        console.log('AJAX Filter: updateProductsStats called', pagination);

        const productsStatsContainer = document.getElementById('products-stats');
        const productsShowingElement = document.getElementById('products-showing');
        const productsTotalElement = document.getElementById('products-total');

        if (!productsStatsContainer || !productsShowingElement || !productsTotalElement) {
            console.warn('AJAX Filter: Products stats elements not found');
            return;
        }

        // Tính toán số sản phẩm đang hiển thị
        const currentPage = pagination.current_page || 1;
        const itemsPerPage = pagination.items_per_page || 12;
        const totalProducts = pagination.total_products || 0;

        // Tính số sản phẩm hiển thị trên trang hiện tại
        const startItem = (currentPage - 1) * itemsPerPage + 1;
        const endItem = Math.min(currentPage * itemsPerPage, totalProducts);
        const showingCount = totalProducts > 0 ? endItem : 0;

        console.log('AJAX Filter: Products stats calculation', {
            currentPage,
            itemsPerPage,
            totalProducts,
            startItem,
            endItem,
            showingCount
        });

        // Cập nhật số liệu với animation
        this.animateNumberChange(productsShowingElement, showingCount);
        this.animateNumberChange(productsTotalElement, totalProducts);

        console.log('AJAX Filter: Products stats updated successfully');
    }

    animateNumberChange(element, newValue) {
        if (!element) return;

        const currentValue = parseInt(element.textContent.replace(/[^\d]/g, '')) || 0;
        const formattedNewValue = new Intl.NumberFormat('vi-VN').format(newValue);

        if (currentValue === newValue) {
            return; // Không thay đổi
        }

        // Fade out
        element.style.transition = 'opacity 0.2s ease';
        element.style.opacity = '0.5';

        // Update value và fade in
        setTimeout(() => {
            element.textContent = formattedNewValue;
            element.style.opacity = '1';
        }, 200);
    }
}

// Khởi tạo AJAX Filter sau khi tất cả script đã load
document.addEventListener('DOMContentLoaded', function() {
    // Delay để đảm bảo tất cả script khác đã chạy xong
    setTimeout(() => {
        console.log('Initializing AJAX Filter...');
        window.ajaxFilter = new AjaxFilter();
        console.log('AJAX Filter initialized successfully');

        // Thêm một delay nữa để override lại sau khi tất cả script khác đã chạy
        setTimeout(() => {
            console.log('AJAX Filter: Final override...');
            if (window.ajaxFilter) {
                window.ajaxFilter.overrideApplyFiltersButton();
            }

            // Đảm bảo hàm removeKeywordFilter được định nghĩa lại
            console.log('🔧 AJAX Filter: Re-defining removeKeywordFilter...');
            window.removeKeywordFilter = function() {
                console.log('🗑️ AJAX Filter: Removing keyword filter');

                // Xóa keyword từ TẤT CẢ search inputs
                const mainSearchInput = document.getElementById('main-search-input');
                if (mainSearchInput) {
                    mainSearchInput.value = '';
                    console.log('✅ Main search input cleared');
                }

                const sidebarSearchInput = document.querySelector('input[name="keyword"], #search-input');
                if (sidebarSearchInput && sidebarSearchInput !== mainSearchInput) {
                    sidebarSearchInput.value = '';
                    console.log('✅ Sidebar search input cleared');
                }

                // Trigger AJAX filter với data mới
                if (window.ajaxFilter) {
                    console.log('🔄 Calling handleRemoveFilter()');
                    window.ajaxFilter.handleRemoveFilter();
                } else {
                    console.log('⚠️ AJAX Filter not available, using fallback');
                    // Fallback: reload trang
                    const url = new URL(window.location);
                    url.searchParams.delete('keyword');
                    window.location.href = url.toString();
                }
            };

            // Thêm event delegation để bắt click trên nút remove keyword (backup)
            document.addEventListener('click', function(e) {
                // Kiểm tra nhiều cách khác nhau
                const isKeywordRemoveBtn =
                    e.target.closest('button[onclick*="removeKeywordFilter"]') ||
                    e.target.closest('.remove-btn') && e.target.closest('.filter-tag') &&
                    e.target.closest('.filter-tag').textContent.includes('Từ khóa:');

                if (isKeywordRemoveBtn) {
                    console.log('🎯 AJAX Filter: Detected click on keyword remove button via delegation');
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();

                    // Gọi hàm remove keyword
                    if (typeof window.removeKeywordFilter === 'function') {
                        window.removeKeywordFilter();
                    }
                    return false;
                }
            }, true); // Use capture phase

            // Override onclick handler trực tiếp
            console.log('🔧 AJAX Filter: Overriding onclick handler for keyword remove button...');
            const keywordRemoveBtn = document.querySelector('button[onclick*="removeKeywordFilter"]');
            if (keywordRemoveBtn) {
                console.log('✅ Found keyword remove button, overriding onclick');
                keywordRemoveBtn.onclick = function(e) {
                    console.log('🎯 AJAX Filter: Keyword remove button clicked (overridden)');
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();

                    if (typeof window.removeKeywordFilter === 'function') {
                        window.removeKeywordFilter();
                    }
                    return false;
                };
            } else {
                console.warn('❌ Keyword remove button not found for override');
            }

            console.log('✅ removeKeywordFilter re-defined and event delegation set up successfully');
        }, 500);
    }, 200);
});

// Global functions để xử lý remove filter từ Filter Results Header với AJAX
window.removeKeywordFilter = function() {
    console.log('🗑️ AJAX Filter: Removing keyword filter');

    // Xóa keyword từ TẤT CẢ search inputs
    const mainSearchInput = document.getElementById('main-search-input');
    if (mainSearchInput) {
        console.log('🔍 Main search input before clear:', mainSearchInput.value);
        mainSearchInput.value = '';
        console.log('✅ Cleared main search input');
    } else {
        console.warn('❌ Main search input not found');
    }

    const sidebarSearchInput = document.querySelector('input[name="keyword"], #search-input');
    if (sidebarSearchInput && sidebarSearchInput !== mainSearchInput) {
        console.log('🔍 Sidebar search input before clear:', sidebarSearchInput.value);
        sidebarSearchInput.value = '';
        console.log('✅ Cleared sidebar search input');
    } else {
        console.log('ℹ️ No separate sidebar search input found');
    }

    // Uncheck keyword checkbox nếu có
    const keywordCheckbox = document.querySelector('input[type="checkbox"][value*="keyword"]');
    if (keywordCheckbox) {
        keywordCheckbox.checked = false;
        console.log('✅ Unchecked keyword checkbox');
    }

    // Trigger AJAX filter với data mới
    if (window.ajaxFilter) {
        console.log('🔄 Triggering AJAX filter after keyword removal');
        window.ajaxFilter.handleRemoveFilter();
    } else {
        console.log('⚠️ AJAX Filter not available, using fallback');
        // Fallback: reload trang
        const url = new URL(window.location);
        url.searchParams.delete('keyword');
        window.location.href = url.toString();
    }
};

window.removeCategoryFilter = function(categoryId) {
    console.log('AJAX Filter: Removing category filter', categoryId);

    // Uncheck category checkbox
    const categoryCheckbox = document.querySelector(`input[name="category[]"][value="${categoryId}"]`);
    if (categoryCheckbox) {
        categoryCheckbox.checked = false;
        console.log('AJAX Filter: Unchecked category checkbox for ID:', categoryId);
    } else {
        console.warn('AJAX Filter: Category checkbox not found for ID:', categoryId);
    }

    // Trigger AJAX filter với data mới
    if (window.ajaxFilter) {
        window.ajaxFilter.handleRemoveFilter();
    } else {
        // Fallback: reload trang
        const url = new URL(window.location);
        const categories = url.searchParams.getAll('category[]');
        const newCategories = categories.filter(id => parseInt(id) !== parseInt(categoryId));

        url.searchParams.delete('category[]');
        newCategories.forEach(id => {
            url.searchParams.append('category[]', id);
        });

        window.location.href = url.toString();
    }
};

window.removePriceFilter = function() {
    console.log('AJAX Filter: Removing price filter');

    // Clear price inputs
    const priceMinInput = document.getElementById('price-min');
    const priceMaxInput = document.getElementById('price-max');

    if (priceMinInput) {
        priceMinInput.value = '';
    }
    if (priceMaxInput) {
        priceMaxInput.value = '';
    }

    // Clear active state từ tất cả price preset buttons
    document.querySelectorAll('.price-preset').forEach(btn => {
        btn.className = btn.className.replace(/bg-gradient-to-r|from-orange-500|to-orange-600|text-white|border-orange-500|shadow-md/g, '');
        btn.classList.add('bg-white', 'text-gray-700', 'border-gray-200');
    });

    // Trigger AJAX filter với data mới
    if (window.ajaxFilter) {
        window.ajaxFilter.handleRemoveFilter();
    } else {
        // Fallback: reload trang
        const url = new URL(window.location);
        url.searchParams.delete('price_min');
        url.searchParams.delete('price_max');
        window.location.href = url.toString();
    }
};

window.removePromotionFilter = function(promotion) {
    console.log('AJAX Filter: Removing promotion filter', promotion);

    // Uncheck promotion checkbox
    const promotionCheckbox = document.querySelector(`input[name="promotion[]"][value="${promotion}"]`);
    if (promotionCheckbox) {
        promotionCheckbox.checked = false;
        console.log('AJAX Filter: Unchecked promotion checkbox for:', promotion);
    } else {
        console.warn('AJAX Filter: Promotion checkbox not found for:', promotion);
    }

    // Trigger AJAX filter với data mới
    if (window.ajaxFilter) {
        window.ajaxFilter.handleRemoveFilter();
    } else {
        // Fallback: reload trang
        const url = new URL(window.location);
        const promotions = url.searchParams.getAll('promotion[]');
        const newPromotions = promotions.filter(p => p !== promotion);

        url.searchParams.delete('promotion[]');
        newPromotions.forEach(p => {
            url.searchParams.append('promotion[]', p);
        });

        window.location.href = url.toString();
    }
};
